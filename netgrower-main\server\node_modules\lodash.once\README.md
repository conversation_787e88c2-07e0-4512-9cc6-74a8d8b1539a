# lodash.once v4.1.1

The [lodash](https://lodash.com/) method `_.once` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.once
```

In Node.js:
```js
var once = require('lodash.once');
```

See the [documentation](https://lodash.com/docs#once) or [package source](https://github.com/lodash/lodash/blob/4.1.1-npm-packages/lodash.once) for more details.
